// Global variables
let isLoading = false;

// DOM elements
const messageInput = document.getElementById('messageInput');
const chatMessages = document.getElementById('chatMessages');
const sendButton = document.getElementById('sendButton');
const loadingOverlay = document.getElementById('loadingOverlay');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Focus on input
    messageInput.focus();
    
    // Add enter key listener
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !isLoading) {
            sendMessage();
        }
    });
    
    // Auto-resize chat messages
    scrollToBottom();
});

// Send message function
async function sendMessage() {
    const message = messageInput.value.trim();
    
    if (!message || isLoading) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    
    // Clear input
    messageInput.value = '';
    
    // Show loading
    showLoading();
    
    try {
        // Send to backend
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Add bot response
            addBotResponse(data.response);
        } else {
            addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        }
        
    } catch (error) {
        console.error('Error:', error);
        addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
    } finally {
        hideLoading();
    }
}

// Quick message function
function sendQuickMessage(message) {
    messageInput.value = message;
    sendMessage();
}

// Add message to chat
function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    const content = document.createElement('div');
    content.className = 'message-content';
    
    const messageText = document.createElement('div');
    messageText.className = 'message-text';
    messageText.innerHTML = text;
    
    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString();
    
    content.appendChild(messageText);
    content.appendChild(messageTime);
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Add bot response with formatting
function addBotResponse(response) {
    let responseText = response.response;
    
    // Format different types of responses
    if (response.data && response.data.success) {
        switch (response.type) {
            case 'time':
                responseText += `
                    <div style="margin-top: 10px; padding: 10px; background: rgba(102, 126, 234, 0.1); border-radius: 8px;">
                        <strong>🕐 ${response.data.formatted}</strong><br>
                        📅 Day: ${response.data.day}<br>
                        📆 Date: ${response.data.date}
                    </div>
                `;
                break;
                
            case 'calculation':
                responseText += `
                    <div style="margin-top: 10px; padding: 10px; background: rgba(40, 167, 69, 0.1); border-radius: 8px;">
                        <strong>🧮 Result: ${response.data.result}</strong><br>
                        📝 Operation: ${response.data.operation}<br>
                        💡 Expression: ${response.data.expression}
                    </div>
                `;
                break;
                
            case 'weather':
                responseText += `
                    <div style="margin-top: 10px; padding: 10px; background: rgba(255, 193, 7, 0.1); border-radius: 8px;">
                        <strong>🌤️ ${response.data.condition}</strong><br>
                        🌡️ Temperature: ${response.data.temperature}<br>
                        📍 Location: ${response.data.location}<br>
                        💧 Humidity: ${response.data.humidity}
                    </div>
                `;
                break;
                
            case 'policy':
                responseText += `
                    <div style="margin-top: 10px; padding: 10px; background: rgba(220, 53, 69, 0.1); border-radius: 8px;">
                        <strong>📋 ${response.data.policy}</strong><br>
                        📝 Details: ${response.data.details}
                    </div>
                `;
                break;
                
            case 'help':
                responseText += `
                    <div style="margin-top: 10px; padding: 10px; background: rgba(102, 126, 234, 0.1); border-radius: 8px;">
                        <strong>🤖 My Capabilities:</strong><br>
                        ${response.data.capabilities.map(cap => `• ${cap}`).join('<br>')}
                    </div>
                `;
                break;
        }
    } else if (response.data && !response.data.success) {
        responseText += `
            <div style="margin-top: 10px; padding: 10px; background: rgba(220, 53, 69, 0.1); border-radius: 8px; color: #dc3545;">
                ❌ ${response.data.error || response.data.message}
            </div>
        `;
    }
    
    addMessage(responseText, 'bot');
}

// Show loading overlay
function showLoading() {
    isLoading = true;
    loadingOverlay.style.display = 'flex';
    sendButton.disabled = true;
}

// Hide loading overlay
function hideLoading() {
    isLoading = false;
    loadingOverlay.style.display = 'none';
    sendButton.disabled = false;
    messageInput.focus();
}

// Scroll to bottom of chat
function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Clear chat function
function clearChat() {
    if (confirm('Are you sure you want to clear the chat?')) {
        chatMessages.innerHTML = `
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">
                        Hello! I'm JARVIS, your AI assistant. I can help you with:
                        <ul>
                            <li>🕐 Current time and date</li>
                            <li>🧮 Mathematical calculations</li>
                            <li>🌤️ Weather information</li>
                            <li>📋 Company policy questions</li>
                        </ul>
                        Try asking me something like "What time is it?" or "Calculate 25 * 4"
                    </div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        `;
    }
}

// Add some demo interactions on load
window.addEventListener('load', function() {
    // Add a welcome animation
    setTimeout(() => {
        const welcomeMessage = "👋 Welcome! I'm ready to assist you. Try the quick action buttons below or type your own question!";
        addMessage(welcomeMessage, 'bot');
    }, 1000);
});

// Add typing indicator (optional enhancement)
function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-indicator';
    typingDiv.id = 'typing-indicator';
    
    typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-text">
                <span class="typing-dots">
                    <span>.</span><span>.</span><span>.</span>
                </span>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    scrollToBottom();
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Add CSS for typing indicator
const style = document.createElement('style');
style.textContent = `
    .typing-dots {
        display: inline-block;
    }
    
    .typing-dots span {
        animation: typing 1.4s infinite;
        animation-fill-mode: both;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes typing {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-10px);
        }
    }
`;
document.head.appendChild(style);
