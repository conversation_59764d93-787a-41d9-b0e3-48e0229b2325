class ConversationalAI {
    constructor() {
        this.sessionId = null;
        this.isTyping = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.startConversation();
    }

    bindEvents() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const clearChat = document.getElementById('clearChat');
        const showAnalytics = document.getElementById('showAnalytics');
        const closeModal = document.getElementById('closeModal');

        // Send message events
        sendButton.addEventListener('click', () => this.sendMessage());
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        // Suggestion buttons
        document.querySelectorAll('.suggestion-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                messageInput.value = btn.dataset.text;
                this.sendMessage();
            });
        });

        // Control buttons
        clearChat.addEventListener('click', () => this.clearChat());
        showAnalytics.addEventListener('click', () => this.showAnalytics());
        closeModal.addEventListener('click', () => this.closeModal());

        // Modal close on outside click
        document.getElementById('analyticsModal').addEventListener('click', (e) => {
            if (e.target.id === 'analyticsModal') this.closeModal();
        });
    }

    async startConversation() {
        try {
            const response = await fetch('/api/start-conversation', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ user_id: 'web_user' })
            });

            const data = await response.json();
            if (data.success) {
                this.sessionId = data.session_id;
                this.showToast('Conversation started!', 'success');
            }
        } catch (error) {
            this.showToast('Failed to start conversation', 'error');
        }
    }

    async sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message || this.isTyping) return;

        // Add user message to chat
        this.addMessage(message, 'user');
        messageInput.value = '';

        // Show typing indicator
        this.showTyping();

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            });

            const data = await response.json();
            
            // Hide typing indicator
            this.hideTyping();

            if (data.success) {
                // Add AI response
                this.addMessage(data.response, 'ai', {
                    intent: data.intent,
                    confidence: data.confidence,
                    entities: data.entities
                });
            } else {
                this.addMessage('Sorry, I encountered an error. Please try again.', 'ai');
                this.showToast(data.error || 'Unknown error', 'error');
            }
        } catch (error) {
            this.hideTyping();
            this.addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'ai');
            this.showToast('Connection error', 'error');
        }
    }

    addMessage(text, sender, metadata = null) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatarIcon = sender === 'ai' ? 'fa-robot' : 'fa-user';
        const currentTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        let metaInfo = '';
        if (metadata && sender === 'ai') {
            const confidence = Math.round(metadata.confidence * 100);
            const entityCount = Object.values(metadata.entities).flat().length;
            metaInfo = `<div class="message-meta">Intent: ${metadata.intent} (${confidence}%) • Entities: ${entityCount}</div>`;
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas ${avatarIcon}"></i>
            </div>
            <div class="message-content">
                <div class="message-text">${text}</div>
                <div class="message-time">${currentTime}</div>
                ${metaInfo}
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    showTyping() {
        this.isTyping = true;
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.style.display = 'flex';
        
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    hideTyping() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.style.display = 'none';
    }

    clearChat() {
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.innerHTML = `
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">
                        Chat cleared! How can I help you today?
                    </div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        `;
        this.startConversation(); // Start new session
        this.showToast('Chat cleared!', 'success');
    }

    async showAnalytics() {
        const modal = document.getElementById('analyticsModal');
        const content = document.getElementById('analyticsContent');
        
        modal.style.display = 'block';
        content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading analytics...</div>';

        try {
            // Get conversation summary
            const summaryResponse = await fetch('/api/conversation-summary');
            const summaryData = await summaryResponse.json();

            // Get system analytics
            const analyticsResponse = await fetch('/api/analytics');
            const analyticsData = await analyticsResponse.json();

            if (summaryData.success && analyticsData.success) {
                content.innerHTML = this.formatAnalytics(summaryData.summary, analyticsData.analytics);
            } else {
                content.innerHTML = '<div class="error">Failed to load analytics</div>';
            }
        } catch (error) {
            content.innerHTML = '<div class="error">Error loading analytics</div>';
        }
    }

    formatAnalytics(summary, analytics) {
        const intentList = Object.entries(analytics.intent_distribution)
            .map(([intent, count]) => `<li>${intent}: ${count}</li>`)
            .join('');

        const entitiesList = Object.entries(summary.entities_found || {})
            .map(([type, entities]) => `<li>${type}: ${entities.join(', ')}</li>`)
            .join('');

        return `
            <div class="analytics-section">
                <h4>Conversation Summary</h4>
                <ul>
                    <li>Messages: ${summary.duration || 0}</li>
                    <li>Current Topic: ${summary.current_topic || 'General'}</li>
                    <li>User: ${summary.user_info?.name || 'Anonymous'}</li>
                </ul>
            </div>
            
            <div class="analytics-section">
                <h4>Intent Distribution</h4>
                <ul>${intentList || '<li>No intents detected yet</li>'}</ul>
            </div>
            
            <div class="analytics-section">
                <h4>Entities Mentioned</h4>
                <ul>${entitiesList || '<li>No entities found yet</li>'}</ul>
            </div>
            
            <div class="analytics-section">
                <h4>System Status</h4>
                <ul>
                    <li>Status: ${analytics.system_status}</li>
                    <li>Active Sessions: ${analytics.active_conversations}</li>
                    <li>Total Sessions: ${analytics.total_sessions}</li>
                </ul>
            </div>
        `;
    }

    closeModal() {
        document.getElementById('analyticsModal').style.display = 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new ConversationalAI();
});
