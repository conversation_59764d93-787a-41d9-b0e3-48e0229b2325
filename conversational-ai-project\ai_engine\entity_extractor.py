"""
Entity Extraction Module
Extracts important information from user messages

THEORY:
- Named Entity Recognition (NER)
- Information Extraction
- Pattern Recognition
- Context-aware extraction
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Any

class EntityExtractor:
    def __init__(self):
        """
        Initialize entity extraction patterns
        
        INTERVIEW POINT: Production systems use:
        - spaCy NER models
        - BERT-based entity extraction
        - Custom trained models
        - Multi-language support
        """
        
        # Common name patterns (Indian context)
        self.name_patterns = [
            r'\b(my name is|i am|i\'m|call me)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',
            r'\b(mr|mrs|ms|dr|prof)\.?\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b'
        ]
        
        # Email patterns
        self.email_patterns = [
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        ]
        
        # Phone number patterns (Indian)
        self.phone_patterns = [
            r'\b(?:\+91|91)?[-.\s]?[6-9]\d{9}\b',
            r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b'
        ]
        
        # Date patterns
        self.date_patterns = [
            r'\b(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})\b',
            r'\b(today|tomorrow|yesterday)\b',
            r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}\b'
        ]
        
        # Time patterns
        self.time_patterns = [
            r'\b(\d{1,2}):(\d{2})\s*(am|pm)?\b',
            r'\b(\d{1,2})\s*(am|pm)\b',
            r'\b(morning|afternoon|evening|night)\b'
        ]
        
        # Number patterns
        self.number_patterns = [
            r'\b\d+(?:\.\d+)?\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten)\b'
        ]
        
        # Location patterns (Indian cities)
        self.location_patterns = [
            r'\b(mumbai|delhi|bangalore|chennai|kolkata|hyderabad|pune|ahmedabad|jaipur|lucknow)\b',
            r'\b(india|bharat)\b',
            r'\bin\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b'
        ]
    
    def extract_entities(self, message: str) -> Dict[str, List[Any]]:
        """
        Extract all entities from message
        
        Args:
            message: User's input message
            
        Returns:
            Dictionary with extracted entities by type
        """
        entities = {
            'names': [],
            'emails': [],
            'phones': [],
            'dates': [],
            'times': [],
            'numbers': [],
            'locations': []
        }
        
        # Extract names
        for pattern in self.name_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                entities['names'].append({
                    'value': match.group(2).strip(),
                    'context': match.group(1),
                    'position': match.span()
                })
        
        # Extract emails
        for pattern in self.email_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                entities['emails'].append({
                    'value': match.group(0),
                    'position': match.span()
                })
        
        # Extract phone numbers
        for pattern in self.phone_patterns:
            matches = re.finditer(pattern, message)
            for match in matches:
                entities['phones'].append({
                    'value': match.group(0),
                    'position': match.span()
                })
        
        # Extract dates
        entities['dates'] = self._extract_dates(message)
        
        # Extract times
        for pattern in self.time_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                entities['times'].append({
                    'value': match.group(0),
                    'position': match.span()
                })
        
        # Extract numbers
        for pattern in self.number_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                entities['numbers'].append({
                    'value': match.group(0),
                    'position': match.span()
                })
        
        # Extract locations
        for pattern in self.location_patterns:
            matches = re.finditer(pattern, message, re.IGNORECASE)
            for match in matches:
                entities['locations'].append({
                    'value': match.group(0) if len(match.groups()) == 0 else match.group(1),
                    'position': match.span()
                })
        
        return entities
    
    def _extract_dates(self, message: str) -> List[Dict]:
        """Extract and normalize dates"""
        dates = []
        
        # Relative dates
        today = datetime.now()
        relative_dates = {
            'today': today,
            'tomorrow': today + timedelta(days=1),
            'yesterday': today - timedelta(days=1)
        }
        
        for word, date_obj in relative_dates.items():
            if word in message.lower():
                dates.append({
                    'value': word,
                    'normalized': date_obj.strftime('%Y-%m-%d'),
                    'type': 'relative'
                })
        
        # Absolute dates
        date_pattern = r'\b(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})\b'
        matches = re.finditer(date_pattern, message)
        for match in matches:
            day, month, year = match.groups()
            if len(year) == 2:
                year = '20' + year
            
            try:
                date_obj = datetime(int(year), int(month), int(day))
                dates.append({
                    'value': match.group(0),
                    'normalized': date_obj.strftime('%Y-%m-%d'),
                    'type': 'absolute',
                    'position': match.span()
                })
            except ValueError:
                # Invalid date
                pass
        
        return dates
    
    def get_entity_summary(self, entities: Dict) -> str:
        """Generate human-readable summary of extracted entities"""
        summary_parts = []
        
        for entity_type, entity_list in entities.items():
            if entity_list:
                count = len(entity_list)
                values = [item['value'] for item in entity_list]
                summary_parts.append(f"{entity_type}: {', '.join(values)} ({count} found)")
        
        return '; '.join(summary_parts) if summary_parts else "No entities found"

# Example usage
if __name__ == "__main__":
    extractor = EntityExtractor()
    
    test_messages = [
        "Hi, my name is Rahul Sharma and my <NAME_EMAIL>",
        "Please call me at 9876543210 tomorrow at 3:30 PM",
        "I live in Mumbai and need help with order number 12345",
        "Schedule meeting for 15/12/2024 in Bangalore office"
    ]
    
    print("=== ENTITY EXTRACTION DEMO ===")
    for msg in test_messages:
        entities = extractor.extract_entities(msg)
        print(f"\nMessage: '{msg}'")
        print(f"Entities: {extractor.get_entity_summary(entities)}")
        
        # Show detailed extraction
        for entity_type, entity_list in entities.items():
            if entity_list:
                print(f"  {entity_type.upper()}:")
                for entity in entity_list:
                    print(f"    - {entity}")
