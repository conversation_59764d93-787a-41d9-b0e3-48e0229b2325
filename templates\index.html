<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS AI Assistant - Smart Agent Demo</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>JARVIS AI Assistant</h1>
                </div>
                <div class="status">
                    <span class="status-indicator"></span>
                    <span>Online</span>
                </div>
            </div>
        </header>

        <!-- Main Chat Interface -->
        <main class="main-content">
            <!-- Capabilities Panel -->
            <div class="capabilities-panel">
                <h3><i class="fas fa-cogs"></i> Capabilities</h3>
                <div class="capability-list">
                    <div class="capability-item">
                        <i class="fas fa-clock"></i>
                        <span>Time & Date</span>
                    </div>
                    <div class="capability-item">
                        <i class="fas fa-calculator"></i>
                        <span>Calculations</span>
                    </div>
                    <div class="capability-item">
                        <i class="fas fa-cloud-sun"></i>
                        <span>Weather Info</span>
                    </div>
                    <div class="capability-item">
                        <i class="fas fa-file-alt"></i>
                        <span>Company Policies</span>
                    </div>
                </div>
            </div>

            <!-- Chat Container -->
            <div class="chat-container">
                <div class="chat-header">
                    <h2><i class="fas fa-comments"></i> Chat with JARVIS</h2>
                    <button class="clear-chat" onclick="clearChat()">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                Hello! I'm JARVIS, your AI assistant. I can help you with:
                                <ul>
                                    <li>🕐 Current time and date</li>
                                    <li>🧮 Mathematical calculations</li>
                                    <li>🌤️ Weather information</li>
                                    <li>📋 Company policy questions</li>
                                </ul>
                                Try asking me something like "What time is it?" or "Calculate 25 * 4"
                            </div>
                            <div class="message-time">Just now</div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="chat-input-container">
                    <div class="chat-input">
                        <input type="text" id="messageInput" placeholder="Type your message here..." autocomplete="off">
                        <button onclick="sendMessage()" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="quick-actions">
                        <button class="quick-btn" onclick="sendQuickMessage('What time is it?')">
                            <i class="fas fa-clock"></i> Time
                        </button>
                        <button class="quick-btn" onclick="sendQuickMessage('Calculate 15 * 8')">
                            <i class="fas fa-calculator"></i> Calculate
                        </button>
                        <button class="quick-btn" onclick="sendQuickMessage('What is the weather?')">
                            <i class="fas fa-cloud"></i> Weather
                        </button>
                        <button class="quick-btn" onclick="sendQuickMessage('What is the leave policy?')">
                            <i class="fas fa-file"></i> Policies
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>
                <i class="fas fa-code"></i> 
                Built with Flask, Python & AI | 
                <i class="fas fa-server"></i> 
                Hosted on Replit | 
                <i class="fas fa-heart"></i> 
                Interview Demo Project
            </p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-cog fa-spin"></i>
            <p>JARVIS is thinking...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
