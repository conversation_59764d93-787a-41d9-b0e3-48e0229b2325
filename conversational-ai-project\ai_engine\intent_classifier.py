"""
Intent Classification Module
Identifies user's intention from their message

THEORY:
- Intent = What user wants to achieve
- Classification = Categorizing user input
- Pattern Matching = Simple but effective approach
- Machine Learning = Advanced approach (for interviews)
"""

import re
from typing import Dict, List, Tuple

class IntentClassifier:
    def __init__(self):
        """
        Initialize intent patterns
        
        INTERVIEW POINT: In production, this would use:
        - Machine Learning models (BERT, RoBERTa)
        - Training data with labeled intents
        - Confidence scores
        """
        self.intent_patterns = {
            'greeting': [
                r'\b(hi|hello|hey|good morning|good afternoon|good evening)\b',
                r'\b(namaste|namaskar)\b',
                r'\b(how are you|what\'s up)\b'
            ],
            'goodbye': [
                r'\b(bye|goodbye|see you|take care)\b',
                r'\b(thanks|thank you|thx)\b.*\b(bye|goodbye)\b',
                r'\b(chat later|talk later)\b'
            ],
            'question': [
                r'\b(what|how|when|where|why|which|who)\b',
                r'\b(can you|could you|would you)\b',
                r'\b(tell me|explain|describe)\b',
                r'\?'
            ],
            'request': [
                r'\b(please|help me|assist me)\b',
                r'\b(i need|i want|i would like)\b',
                r'\b(show me|give me|provide)\b'
            ],
            'complaint': [
                r'\b(problem|issue|error|bug|not working)\b',
                r'\b(frustrated|angry|disappointed)\b',
                r'\b(fix|solve|resolve)\b'
            ],
            'compliment': [
                r'\b(good|great|excellent|awesome|amazing)\b',
                r'\b(thank you|thanks|appreciate)\b',
                r'\b(love|like|enjoy)\b'
            ],
            'information': [
                r'\b(about|information|details|data)\b',
                r'\b(company|business|service|product)\b',
                r'\b(policy|rule|guideline)\b'
            ]
        }
        
        # Intent priorities (higher number = higher priority)
        self.intent_priority = {
            'greeting': 3,
            'goodbye': 3,
            'question': 2,
            'request': 2,
            'complaint': 4,
            'compliment': 1,
            'information': 2
        }
    
    def classify_intent(self, message: str) -> Dict:
        """
        Classify user intent from message
        
        Args:
            message: User's input message
            
        Returns:
            Dict with intent, confidence, and matched patterns
            
        INTERVIEW POINT: Real systems use:
        - Neural networks for classification
        - Confidence scores (0-1)
        - Multiple intent detection
        - Fallback mechanisms
        """
        message = message.lower().strip()
        intent_scores = {}
        matched_patterns = {}
        
        # Check each intent pattern
        for intent, patterns in self.intent_patterns.items():
            score = 0
            matches = []
            
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    score += 1
                    matches.append(pattern)
            
            if score > 0:
                # Apply priority weighting
                weighted_score = score * self.intent_priority.get(intent, 1)
                intent_scores[intent] = weighted_score
                matched_patterns[intent] = matches
        
        # Determine primary intent
        if intent_scores:
            primary_intent = max(intent_scores.keys(), key=lambda x: intent_scores[x])
            confidence = min(intent_scores[primary_intent] / 10.0, 1.0)  # Normalize to 0-1
        else:
            primary_intent = 'unknown'
            confidence = 0.0
        
        return {
            'intent': primary_intent,
            'confidence': confidence,
            'all_intents': intent_scores,
            'matched_patterns': matched_patterns.get(primary_intent, []),
            'message': message
        }
    
    def get_intent_description(self, intent: str) -> str:
        """Get human-readable description of intent"""
        descriptions = {
            'greeting': 'User is greeting or starting conversation',
            'goodbye': 'User is ending conversation or saying thanks',
            'question': 'User is asking a question',
            'request': 'User is requesting help or action',
            'complaint': 'User has a problem or complaint',
            'compliment': 'User is giving positive feedback',
            'information': 'User wants information about something',
            'unknown': 'Intent could not be determined'
        }
        return descriptions.get(intent, 'Unknown intent type')

# Example usage and testing
if __name__ == "__main__":
    classifier = IntentClassifier()
    
    test_messages = [
        "Hello, how are you?",
        "Can you help me with my account?",
        "What is your company policy?",
        "Thank you so much!",
        "I have a problem with my order",
        "Goodbye, see you later"
    ]
    
    print("=== INTENT CLASSIFICATION DEMO ===")
    for msg in test_messages:
        result = classifier.classify_intent(msg)
        print(f"\nMessage: '{msg}'")
        print(f"Intent: {result['intent']} (confidence: {result['confidence']:.2f})")
        print(f"Description: {classifier.get_intent_description(result['intent'])}")
