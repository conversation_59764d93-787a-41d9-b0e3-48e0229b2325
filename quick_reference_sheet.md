# 🚀 Quick Reference Sheet - Generative AI Models

## 📝 **One-Minute Summaries**

### **GPT-4**
- **What**: Large language model that understands text + images
- **Size**: ~1.7 trillion parameters
- **Best For**: Conversations, code, reasoning, image understanding
- **Key Feature**: Multimodal (text + vision)
- **Limitation**: Can hallucinate, expensive

### **PaLM**
- **What**: Google's reasoning-focused language model
- **Size**: 540 billion parameters
- **Best For**: Math, step-by-step reasoning, code
- **Key Feature**: Excellent logical thinking
- **Limitation**: Less accessible than GPT-4

### **Stable Diffusion**
- **What**: Text-to-image generation model
- **Process**: Removes noise guided by text
- **Best For**: Creating images, art, design
- **Key Feature**: Open source, customizable
- **Limitation**: Needs good prompts, can be slow

---

## 🎯 **Interview Cheat Sheet**

### **Must-Know Facts:**

#### **GPT-4:**
- Uses transformer architecture with attention
- Trained with RLHF (Reinforcement Learning from Human Feedback)
- Context window: 8K-128K tokens
- Multimodal: Can process images + text
- API available through OpenAI

#### **PaLM:**
- Uses Google's Pathways architecture
- 540B parameters, trained on 780B tokens
- Excellent at chain-of-thought reasoning
- Powers Google Bard
- Available through Google Cloud Vertex AI

#### **Stable Diffusion:**
- Uses diffusion process (noise → image)
- Works in latent space for efficiency
- Components: Text Encoder (CLIP) + U-Net + VAE
- Open source, runs on consumer GPUs
- Versions: 1.5, 2.1, SDXL

### **Common Questions & Answers:**

**Q: "How does attention work in transformers?"**
**A:** "Attention lets the model focus on relevant parts of input. It calculates similarity between query and keys, then weighs values accordingly. Like highlighting important words when reading."

**Q: "What's the difference between GPT-4 and PaLM?"**
**A:** "GPT-4 is multimodal and conversational, PaLM excels at reasoning and math. GPT-4 has more parameters but PaLM is more efficient per parameter."

**Q: "How does Stable Diffusion generate images?"**
**A:** "Starts with noise, gradually removes it guided by text prompt. Uses U-Net to predict noise to remove at each step, working in compressed latent space."

**Q: "What are the main challenges?"**
**A:** "Hallucination, computational cost, prompt sensitivity, safety concerns, and scaling for production use."

---

## 🛠 **Technical Terms to Know**

### **General AI:**
- **Parameters**: Numbers the model learns (like brain connections)
- **Tokens**: Pieces of text the model processes
- **Temperature**: Controls randomness (0=deterministic, 1=creative)
- **Context Window**: How much text the model can remember
- **Fine-tuning**: Training on specific data for specialized tasks

### **Transformers:**
- **Attention**: Mechanism to focus on relevant information
- **Multi-head**: Multiple attention mechanisms working together
- **Encoder-Decoder**: Two-part architecture (BERT vs GPT style)
- **Positional Encoding**: Tells model the order of words
- **Layer Normalization**: Stabilizes training

### **Diffusion Models:**
- **Denoising**: Removing noise step by step
- **Latent Space**: Compressed representation of images
- **Guidance Scale**: How closely to follow the text prompt
- **Inference Steps**: Number of denoising steps
- **Scheduler**: Algorithm for removing noise

---

## 💡 **Practical Tips**

### **For GPT-4:**
```python
# Good prompt structure
system_prompt = "You are an expert in [domain]. Be concise and accurate."
user_prompt = "Task: [specific task]\nContext: [relevant info]\nFormat: [desired output]"
```

### **For Stable Diffusion:**
```python
# Good prompt structure
prompt = "Subject, Style, Quality modifiers, Technical specs"
# Example: "Portrait of a cat, oil painting style, highly detailed, 4K resolution"
```

### **For PaLM:**
```python
# Good for reasoning
prompt = "Let's think step by step:\n1. [problem]\n2. [approach]\n3. [solution]"
```

---

## 🎪 **Demo Ideas for Interview**

### **Simple Projects to Mention:**

1. **Content Generator:**
   - GPT-4 writes blog post
   - Stable Diffusion creates featured image
   - Show understanding of multi-modal workflows

2. **Code Assistant:**
   - GPT-4 generates code
   - Explains functionality
   - Creates documentation

3. **Creative Tool:**
   - User describes concept
   - GPT-4 refines description
   - Stable Diffusion visualizes it

### **Architecture You Could Propose:**
```
User Input → GPT-4 (text processing) → Stable Diffusion (image) → Combined Output
     ↓
PaLM (quality check/reasoning)
```

---

## 🚨 **Red Flags to Avoid**

### **Don't Say:**
- "AI will replace all humans"
- "These models are perfect"
- "I don't know the limitations"
- "Bigger is always better"

### **Do Say:**
- "These are powerful tools with specific use cases"
- "Important to consider safety and ethics"
- "Need human oversight and validation"
- "Efficiency matters as much as capability"

---

## 📚 **Key Resources to Mention**

### **Papers:**
- "Attention Is All You Need" (Transformer paper)
- "PaLM: Scaling Language Modeling with Pathways"
- "High-Resolution Image Synthesis with Latent Diffusion Models"

### **Frameworks:**
- **Hugging Face**: For model access and fine-tuning
- **OpenAI API**: For GPT-4 integration
- **Diffusers**: For Stable Diffusion implementation
- **LangChain**: For building AI applications

### **Concepts to Research Further:**
- Retrieval Augmented Generation (RAG)
- LoRA (Low-Rank Adaptation)
- Constitutional AI
- Chain-of-Thought prompting
- In-context learning

---

## ⚡ **Last-Minute Review**

### **30 seconds before interview:**
1. **GPT-4**: Multimodal transformer, 1.7T params, great for conversation
2. **PaLM**: Google's 540B param model, excellent reasoning
3. **Stable Diffusion**: Text-to-image via denoising, open source
4. **All have**: Scaling challenges, safety concerns, need optimization
5. **Applications**: Content creation, code generation, creative tools

### **Confidence Boosters:**
- You understand the core concepts
- You know practical applications
- You're aware of limitations
- You can discuss real-world implementation
- You're prepared for technical questions

**Remember**: It's okay to say "I don't know but I'd research X" - shows learning mindset!
