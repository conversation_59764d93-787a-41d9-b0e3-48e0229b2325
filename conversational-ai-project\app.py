"""
Conversational AI Web Application
Flask-based web interface for conversational AI system

INTERVIEW POINTS:
- RESTful API design
- Session management
- Real-time communication
- Error handling
- Scalable architecture
"""

from flask import Flask, render_template, request, jsonify, session
from datetime import datetime
import os
import json
from ai_engine.conversation_manager import ConversationManager

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'conversational-ai-secret-key-2024'

# Initialize AI components
conversation_manager = ConversationManager()

# Store active sessions (in production, use Redis/Database)
active_sessions = {}

@app.route('/')
def home():
    """Main chat interface"""
    return render_template('index.html')

@app.route('/api/start-conversation', methods=['POST'])
def start_conversation():
    """
    Start a new conversation session
    
    INTERVIEW POINT: Session management in web applications
    - Session creation and tracking
    - User identification
    - State persistence
    """
    try:
        data = request.get_json() or {}
        user_id = data.get('user_id', 'anonymous')
        
        # Create new conversation session
        session_id = conversation_manager.start_conversation(user_id)
        
        # Store in Flask session
        session['conversation_id'] = session_id
        active_sessions[session_id] = {
            'user_id': user_id,
            'start_time': datetime.now().isoformat(),
            'last_activity': datetime.now().isoformat()
        }
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'message': 'Conversation started successfully',
            'welcome_message': 'Hello! I\'m your AI assistant. How can I help you today?'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    """
    Process chat message and return AI response
    
    INTERVIEW POINT: API design for conversational systems
    - Request/response structure
    - Error handling
    - Performance considerations
    """
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({
                'success': False,
                'error': 'Message is required'
            }), 400
        
        user_message = data['message'].strip()
        if not user_message:
            return jsonify({
                'success': False,
                'error': 'Message cannot be empty'
            }), 400
        
        # Get or create session
        session_id = session.get('conversation_id')
        if not session_id:
            session_id = conversation_manager.start_conversation()
            session['conversation_id'] = session_id
        
        # Update last activity
        if session_id in active_sessions:
            active_sessions[session_id]['last_activity'] = datetime.now().isoformat()
        
        # Process message through AI engine
        ai_response = conversation_manager.process_message(session_id, user_message)
        
        return jsonify({
            'success': True,
            'response': ai_response['response'],
            'intent': ai_response['intent'],
            'confidence': ai_response['confidence'],
            'entities': ai_response['entities'],
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Processing error: {str(e)}'
        }), 500

@app.route('/api/conversation-summary')
def get_conversation_summary():
    """
    Get summary of current conversation
    
    INTERVIEW POINT: Analytics and conversation insights
    - Conversation tracking
    - User behavior analysis
    - Performance metrics
    """
    try:
        session_id = session.get('conversation_id')
        
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'No active conversation'
            }), 400
        
        summary = conversation_manager.get_conversation_summary(session_id)
        
        return jsonify({
            'success': True,
            'summary': summary
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analytics')
def get_analytics():
    """
    Get system analytics and metrics
    
    INTERVIEW POINT: System monitoring and analytics
    - Performance metrics
    - Usage statistics
    - System health monitoring
    """
    try:
        total_sessions = len(active_sessions)
        active_conversations = len([s for s in active_sessions.values() 
                                  if (datetime.now() - datetime.fromisoformat(s['last_activity'])).seconds < 3600])
        
        # Get intent distribution from conversation manager
        all_intents = []
        for conv_id, conv_data in conversation_manager.conversations.items():
            all_intents.extend(conv_data['metadata']['intents_detected'])
        
        intent_counts = {}
        for intent in all_intents:
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
        
        return jsonify({
            'success': True,
            'analytics': {
                'total_sessions': total_sessions,
                'active_conversations': active_conversations,
                'intent_distribution': intent_counts,
                'system_status': 'healthy',
                'timestamp': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health')
def health_check():
    """
    System health check endpoint
    
    INTERVIEW POINT: System reliability and monitoring
    - Health checks
    - Service availability
    - Dependency monitoring
    """
    try:
        # Test AI components
        test_session = conversation_manager.start_conversation('health_check')
        test_response = conversation_manager.process_message(test_session, 'hello')
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'components': {
                'conversation_manager': 'operational',
                'intent_classifier': 'operational',
                'entity_extractor': 'operational'
            },
            'test_response': test_response['response']
        })
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    print("🚀 Starting Conversational AI Application...")
    print("📚 INTERVIEW POINTS COVERED:")
    print("   - Intent Classification")
    print("   - Entity Extraction") 
    print("   - Dialog Management")
    print("   - Context Tracking")
    print("   - Session Management")
    print("   - RESTful API Design")
    print("   - Error Handling")
    print("   - System Analytics")
    print("\n🌐 Access the application at: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
