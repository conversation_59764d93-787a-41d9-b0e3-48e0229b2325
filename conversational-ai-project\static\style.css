* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.chat-header {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.ai-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.ai-info h2 {
    margin: 0;
    font-size: 20px;
}

.status {
    font-size: 12px;
    opacity: 0.8;
}

.status.online::before {
    content: "●";
    color: #10b981;
    margin-right: 5px;
}

.chat-controls {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.btn-icon {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-icon:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

/* Messages */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8fafc;
}

.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
}

.ai-message .message-avatar {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-text {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    word-wrap: break-word;
}

.user-message .message-text {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
}

.message-time {
    font-size: 11px;
    color: #64748b;
    margin-top: 4px;
    padding: 0 16px;
}

.message-meta {
    font-size: 10px;
    color: #64748b;
    margin-top: 4px;
    padding: 0 16px;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 20px;
}

.typing-dots {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #64748b;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

/* Input Area */
.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e2e8f0;
}

.chat-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

#messageInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s;
}

#messageInput:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.send-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.input-suggestions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.suggestion-btn {
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    color: #64748b;
}

.suggestion-btn:hover {
    background: #4f46e5;
    color: white;
    transform: translateY(-2px);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

/* Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    animation: slideInRight 0.3s ease;
}

.toast.error {
    background: #ef4444;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .chat-container {
        width: 100%;
        height: 100vh;
        border-radius: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .input-suggestions {
        display: none;
    }
}
