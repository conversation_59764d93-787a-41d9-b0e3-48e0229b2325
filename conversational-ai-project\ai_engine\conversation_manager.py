"""
Conversation Manager Mo<PERSON>le
Manages dialog flow, context, and response generation

THEORY:
- Dialog Management
- Context Tracking
- State Management
- Response Generation
- Conversation Flow Control
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from .intent_classifier import IntentClassifier
from .entity_extractor import EntityExtractor

class ConversationManager:
    def __init__(self):
        """
        Initialize conversation manager
        
        INTERVIEW POINT: Production systems include:
        - Session management
        - User profiling
        - Conversation analytics
        - Multi-channel support
        """
        self.intent_classifier = IntentClassifier()
        self.entity_extractor = EntityExtractor()
        
        # Conversation state
        self.conversations = {}  # session_id -> conversation_data
        
        # Response templates by intent
        self.response_templates = {
            'greeting': [
                "Hello! I'm your AI assistant. How can I help you today?",
                "Hi there! Great to see you. What can I do for you?",
                "Welcome! I'm here to assist you. What would you like to know?",
                "Hello! Ready to help you with anything you need."
            ],
            'goodbye': [
                "Goodbye! Have a wonderful day!",
                "Thank you for chatting with me. Take care!",
                "See you later! Feel free to come back anytime.",
                "Bye! It was great helping you today."
            ],
            'question': [
                "That's a great question! Let me help you with that.",
                "I'd be happy to answer that for you.",
                "Let me provide you with the information you need.",
                "Good question! Here's what I can tell you:"
            ],
            'request': [
                "I'll be glad to help you with that request.",
                "Let me assist you with that right away.",
                "I'm here to help! Let me take care of that for you.",
                "Absolutely! I can help you with that."
            ],
            'complaint': [
                "I understand your concern and I'm here to help resolve this.",
                "I'm sorry to hear about this issue. Let me help you fix it.",
                "I apologize for the inconvenience. Let's work together to solve this.",
                "I take your feedback seriously. How can I help make this right?"
            ],
            'compliment': [
                "Thank you so much! I really appreciate your kind words.",
                "That's very nice of you to say! I'm glad I could help.",
                "Thank you! It makes me happy to know I was helpful.",
                "I appreciate the feedback! I'm here whenever you need assistance."
            ],
            'information': [
                "I'd be happy to provide you with that information.",
                "Let me share the details you're looking for.",
                "Here's the information you requested:",
                "I can definitely help you with information about that."
            ],
            'unknown': [
                "I'm not sure I understand. Could you please rephrase that?",
                "I'd like to help, but I need a bit more clarification.",
                "Could you tell me more about what you're looking for?",
                "I want to make sure I understand correctly. Can you elaborate?"
            ]
        }
        
        # Conversation context tracking
        self.context_keywords = {
            'personal_info': ['name', 'email', 'phone', 'address'],
            'business': ['company', 'work', 'job', 'career'],
            'technical': ['computer', 'software', 'app', 'website'],
            'support': ['help', 'problem', 'issue', 'error'],
            'general': ['weather', 'time', 'date', 'news']
        }
    
    def start_conversation(self, user_id: str = None) -> str:
        """Start a new conversation session"""
        session_id = str(uuid.uuid4())
        
        self.conversations[session_id] = {
            'session_id': session_id,
            'user_id': user_id,
            'start_time': datetime.now().isoformat(),
            'messages': [],
            'context': {
                'user_name': None,
                'user_email': None,
                'current_topic': None,
                'entities_mentioned': {},
                'conversation_stage': 'greeting'
            },
            'metadata': {
                'message_count': 0,
                'intents_detected': [],
                'entities_extracted': []
            }
        }
        
        return session_id
    
    def process_message(self, session_id: str, user_message: str) -> Dict[str, Any]:
        """
        Process user message and generate response
        
        Args:
            session_id: Conversation session ID
            user_message: User's input message
            
        Returns:
            Response with intent, entities, and generated reply
        """
        if session_id not in self.conversations:
            session_id = self.start_conversation()
        
        conversation = self.conversations[session_id]
        
        # Classify intent
        intent_result = self.intent_classifier.classify_intent(user_message)
        
        # Extract entities
        entities = self.entity_extractor.extract_entities(user_message)
        
        # Update conversation context
        self._update_context(conversation, intent_result, entities, user_message)
        
        # Generate response
        response = self._generate_response(conversation, intent_result, entities)
        
        # Store message in conversation history
        message_data = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'intent': intent_result,
            'entities': entities,
            'ai_response': response,
            'context_snapshot': conversation['context'].copy()
        }
        
        conversation['messages'].append(message_data)
        conversation['metadata']['message_count'] += 1
        
        return {
            'session_id': session_id,
            'response': response,
            'intent': intent_result['intent'],
            'confidence': intent_result['confidence'],
            'entities': entities,
            'context': conversation['context']
        }
    
    def _update_context(self, conversation: Dict, intent_result: Dict, entities: Dict, user_message: str):
        """Update conversation context with new information"""
        context = conversation['context']
        
        # Update user information from entities
        if entities['names']:
            context['user_name'] = entities['names'][0]['value']
        
        if entities['emails']:
            context['user_email'] = entities['emails'][0]['value']
        
        # Track entities mentioned
        for entity_type, entity_list in entities.items():
            if entity_list:
                if entity_type not in context['entities_mentioned']:
                    context['entities_mentioned'][entity_type] = []
                context['entities_mentioned'][entity_type].extend([e['value'] for e in entity_list])
        
        # Determine conversation topic
        context['current_topic'] = self._determine_topic(user_message)
        
        # Update conversation stage
        context['conversation_stage'] = self._determine_stage(intent_result['intent'], conversation['metadata']['message_count'])
        
        # Track intents and entities for analytics
        conversation['metadata']['intents_detected'].append(intent_result['intent'])
        conversation['metadata']['entities_extracted'].append(entities)
    
    def _determine_topic(self, message: str) -> str:
        """Determine conversation topic from message content"""
        message_lower = message.lower()
        
        for topic, keywords in self.context_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                return topic
        
        return 'general'
    
    def _determine_stage(self, intent: str, message_count: int) -> str:
        """Determine current conversation stage"""
        if message_count == 1:
            return 'opening'
        elif intent == 'goodbye':
            return 'closing'
        elif intent in ['question', 'request', 'complaint']:
            return 'problem_solving'
        else:
            return 'ongoing'
    
    def _generate_response(self, conversation: Dict, intent_result: Dict, entities: Dict) -> str:
        """Generate contextual response based on intent and entities"""
        intent = intent_result['intent']
        context = conversation['context']
        
        # Get base response template
        templates = self.response_templates.get(intent, self.response_templates['unknown'])
        base_response = templates[0]  # For simplicity, using first template
        
        # Personalize response if we know user's name
        if context['user_name'] and intent in ['greeting', 'compliment']:
            base_response = f"Hi {context['user_name']}! " + base_response
        
        # Add context-specific information
        if intent == 'question' and entities['dates']:
            base_response += f" I see you mentioned {entities['dates'][0]['value']}."
        
        if intent == 'request' and entities['locations']:
            base_response += f" I understand you're asking about {entities['locations'][0]['value']}."
        
        # Add follow-up based on conversation stage
        if context['conversation_stage'] == 'opening':
            base_response += " What specific information are you looking for?"
        
        return base_response
    
    def get_conversation_summary(self, session_id: str) -> Dict[str, Any]:
        """Get summary of conversation"""
        if session_id not in self.conversations:
            return {"error": "Conversation not found"}
        
        conversation = self.conversations[session_id]
        
        return {
            'session_id': session_id,
            'duration': conversation['metadata']['message_count'],
            'intents_detected': list(set(conversation['metadata']['intents_detected'])),
            'entities_found': conversation['context']['entities_mentioned'],
            'current_topic': conversation['context']['current_topic'],
            'user_info': {
                'name': conversation['context']['user_name'],
                'email': conversation['context']['user_email']
            }
        }

# Example usage
if __name__ == "__main__":
    manager = ConversationManager()
    
    # Start conversation
    session = manager.start_conversation("user123")
    print(f"Started conversation: {session}")
    
    # Test conversation flow
    test_messages = [
        "Hello, my name is Priya",
        "Can you help me with my account?",
        "I have a problem with my order from yesterday",
        "Thank you so much for your help!",
        "Goodbye!"
    ]
    
    print("\n=== CONVERSATION DEMO ===")
    for msg in test_messages:
        result = manager.process_message(session, msg)
        print(f"\nUser: {msg}")
        print(f"AI ({result['intent']}, {result['confidence']:.2f}): {result['response']}")
    
    # Show conversation summary
    summary = manager.get_conversation_summary(session)
    print(f"\n=== CONVERSATION SUMMARY ===")
    print(json.dumps(summary, indent=2))
