/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 2rem;
    color: #667eea;
}

.logo h1 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
}

.status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #28a745;
    font-weight: 500;
}

.status-indicator {
    width: 10px;
    height: 10px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    padding: 2rem;
}

/* Capabilities Panel */
.capabilities-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.capabilities-panel h3 {
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.capability-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.capability-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.capability-item:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateX(5px);
}

.capability-item i {
    color: #667eea;
    width: 20px;
}

/* Chat Container */
.chat-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    height: 600px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chat-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clear-chat {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-chat:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.message-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 15px;
    position: relative;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.message-text {
    line-height: 1.5;
}

.message-text ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-time {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 0.5rem;
}

/* Chat Input */
.chat-input-container {
    padding: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.chat-input input {
    flex: 1;
    padding: 1rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 25px;
    outline: none;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.chat-input input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chat-input button {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-input button:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-btn {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    color: #667eea;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quick-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    text-align: center;
    color: #666;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .capabilities-panel {
        order: 2;
    }
    
    .chat-container {
        order: 1;
        height: 500px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .quick-actions {
        justify-content: center;
    }
}
